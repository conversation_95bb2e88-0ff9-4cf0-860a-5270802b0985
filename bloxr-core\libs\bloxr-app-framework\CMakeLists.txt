include(GenerateExportHeader)

file(GLOB_RECURSE SOURCES "src/*.cpp" "src/*.h" "include/*.h")

add_library(BloxrAppFramework STATIC ${SOURCES}
  src/platform/sdl/SDLWindow.cpp)

target_include_directories(
  BloxrAppFramework
  PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
         $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}>
         $<INSTALL_INTERFACE:include>)

target_link_libraries(
  BloxrAppFramework
  PUBLIC Diligent-Common
         Diligent-GraphicsTools
         Diligent-TargetPlatform
         Diligent-GraphicsEngineD3D11-shared
         Diligent-GraphicsEngineOpenGL-shared
         Diligent-GraphicsEngineD3D12-shared
         Diligent-GraphicsEngineVk-shared
         SDL3::SDL3-shared
         spdlog::spdlog)
