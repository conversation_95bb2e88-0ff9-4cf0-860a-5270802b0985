#ifndef BLOXR_APP_LAYER_APPLICATION_LAYER_STACK_H
#define BLOXR_APP_LAYER_APPLICATION_LAYER_STACK_H

#include <concepts>
#include <iterator>
#include <memory>
#include <vector>

#include "ApplicationLayer.h"

namespace bloxr {
namespace app {
namespace layer {

/**
 * @brief Simple wrapper for `std::vector<ApplicationLayer>`. The
 * implementation may change in the future.
 */
class ApplicationLayerStack {
 public:
  ApplicationLayerStack();
  ~ApplicationLayerStack();

  template <typename T>
    requires std::derived_from<T, ApplicationLayer>
  void PushLayer() {
    layer_stack_.emplace_back(std::make_shared<T>())->OnAttach();
  }
  template <typename T>
    requires std::derived_from<T, ApplicationLayer>
  void PushLayer(const std::shared_ptr<T>& layer) {
    layer_stack_.emplace_back(layer);
    layer->OnAttach();
  }

  using Container = std::vector<std::shared_ptr<ApplicationLayer>>;
  using iterator = Container::iterator;
  using const_iterator = Container::const_iterator;
  using reverse_iterator = Container::reverse_iterator;

  iterator begin() { return layer_stack_.begin(); }
  iterator end() { return layer_stack_.end(); }
  const_iterator begin() const { return layer_stack_.begin(); }
  const_iterator end() const { return layer_stack_.end(); }

  reverse_iterator rbegin() { return layer_stack_.rbegin(); }
  reverse_iterator rend() { return layer_stack_.rend(); }

 private:
  Container layer_stack_;
};

}  // namespace layer
}  // namespace app
}  // namespace bloxr

#endif