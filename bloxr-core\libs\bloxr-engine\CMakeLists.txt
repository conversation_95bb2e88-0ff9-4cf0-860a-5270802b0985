include(GenerateExportHeader)

cpmaddpackage("gh:DiligentGraphics/DiligentCore#API256008")

file(GLOB_RECURSE SOURCES "src/*.cpp" "src/*.h" "include/*.h")

add_library(BloxrEngine SHARED ${SOURCES})

generate_export_header(
  BloxrEngine
  BASE_NAME
  BloxrEngine
  EXPORT_MACRO_NAME
  BLOXR_ENGINE_EXPORT
  EXPORT_FILE_NAME
  ${CMAKE_CURRENT_BINARY_DIR}/export.h)

target_include_directories(
  BloxrEngine
  PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
         $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}>
         $<INSTALL_INTERFACE:include>)

target_link_libraries(
  BloxrEngine
  PUBLIC Diligent-Common
         Diligent-GraphicsTools
         Diligent-TargetPlatform
         Diligent-GraphicsEngineD3D11-shared
         Diligent-GraphicsEngineOpenGL-shared
         Diligent-GraphicsEngineD3D12-shared
         Diligent-GraphicsEngineVk-shared
         SDL3::SDL3-shared
         spdlog::spdlog)
