#ifndef BLOXR_APP_PLATFORM_WINDOW_H
#define BLOXR_APP_PLATFORM_WINDOW_H

#include <expected>
#include <string>

#include <glm/glm.hpp>

#include "event/Event.h"
#include "event/EventDispatcher.h"
#include "event/EventTypes.h"

namespace bloxr {
namespace app {
namespace platform {

enum class WindowError {
  WindowCreationFailed,
};

enum class WindowMode {
  Windowed,
  Fullscreen,
  Borderless,
};

struct WindowSpec {
  std::string title = "Bloxr Window";
  glm::uvec2 size = { 1280, 720 };
  glm::uvec2 min_size = { 320, 240 };
  bool resizable = true;
  bool decorated = true;
  WindowMode mode = WindowMode::Windowed;
};

class Window {
 public:
  virtual ~Window() = default;

  virtual std::expected<void, WindowError> Init(const struct WindowSpec&) = 0;
  virtual void Shutdown() = 0;

  virtual void PollEvents() = 0;
  virtual void SwapBuffers() = 0;

  virtual bool ShouldClose() const = 0;
  virtual void SetShouldClose(bool value = true) = 0;

  virtual glm::ivec2 Position() const = 0;
  virtual void SetPosition(glm::ivec2) = 0;

  virtual glm::uvec2 Size() const = 0;
  virtual void SetSize(glm::uvec2) = 0;

  virtual glm::uvec2 FramebufferSize() const = 0;

  virtual std::string Title() const = 0;
  virtual void SetTitle(std::string_view) = 0;

  virtual WindowMode Mode() const = 0;
  virtual void SetMode(WindowMode) = 0;

  virtual void SetBackgroundColor(glm::vec4 color) = 0;

  float Aspect() const {
    const auto sz = Size();
    return sz.x == 0 ? 1.0f : static_cast<float>(sz.x) / sz.y;
  }
};

}  // namespace platform
}  // namespace app
}  // namespace bloxr

#endif