#ifndef BLOXR_APP_LAYER_APPLICATION_LAYER_H
#define BLOXR_APP_LAYER_APPLICATION_LAYER_H

#include <string>

#include "../event/Event.h"

namespace bloxr {
namespace app {
namespace layer {

class ApplicationLayer {
 public:
  virtual ~ApplicationLayer() = default;

  virtual void OnAttach() {}
  virtual void OnDetach() {}
  virtual void OnUpdate(float delta_time) {}
  virtual void OnRender() {}
  virtual void OnEvent(const bloxr::app::event::Event& event) {}

  const std::string& GetName() const { return name_; }

 protected:
  std::string name_;
};

}  // namespace layer
}  // namespace app
}  // namespace bloxr

#endif