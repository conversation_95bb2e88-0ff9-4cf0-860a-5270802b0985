# TODO

* Implement Event and Event Dispatcher
  * Event requires only one member variable ```Handled```
  * EventDispatcher has to provide functionality that allows for Event Listening
* Implement SDLWindow Class
* Implement Application Class
  * Switch from using SDL_Window class to the ABC Window Wrapper
* Fix Dependencies, Compile
  * Expected Result is Black SDL Window
* Attach DiligentEngine Render context, Write the Render Module
  * In the Engine SubModule, write a simple layer that renders the Hello Triangle
* Fix Dependencies, Compile with Emscripten ToolChain
