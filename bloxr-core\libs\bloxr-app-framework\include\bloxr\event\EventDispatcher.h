#ifndef BLOXR_APP_EVENT_EVENT_DISPATCHER_H
#define BLOXR_APP_EVENT_EVENT_DISPATCHER_H

#include <algorithm>
#include <concepts>
#include <functional>
#include <vector>

#include "Event.h"

namespace bloxr {
namespace app {
namespace event {

template <typename T>
  requires std::derived_from<T, Event>
class EventDispatcher {
 public:
  using Listener = std::function<void(T&)>;
  using ListenerId = std::size_t;
  using ListenerEntry = std::pair<ListenerId, Listener>;

  ListenerId RegisterListener(Listener listener) {
    const ListenerId id = next_id_++;
    listeners_.emplace_back(id, std::move(listener));
    return id;
  }

  void UnregisterListener(ListenerId id) {
    std::vector<ListenerEntry>::iterator it = std::remove_if(
        listeners_.begin(), listeners_.end(),
        [id](const ListenerEntry& pair) { return pair.first == id; });
    listeners_.erase(it, listeners_.end());
  }

  void UnregisterAllListeners() { listeners_.clear(); }

  bool Dispatch(T& event) {
    std::vector<ListenerEntry> snapshot = listeners_;
    for (auto& [id, fn] : snapshot) {
      fn(event);
      if (event.Handled()) return true;
    }
    return event.Handled();
  }

 private:
  std::vector<ListenerEntry> listeners_;
  ListenerId next_id_ = 0;
};

}  // namespace event
}  // namespace app
}  // namespace bloxr

#endif