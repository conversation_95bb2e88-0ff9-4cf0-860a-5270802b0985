cmake_minimum_required(VERSION 4.0.0)
project(Bloxr)

set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR})

include(cmake/CPM.cmake)

cpmaddpackage("gh:gabime/spdlog@1.15.3")
cpmaddpackage("gh:Neargye/magic_enum@0.9.7")
cpmaddpackage("gh:libsdl-org/SDL#release-3.2.18")
cpmaddpackage("gh:DiligentGraphics/DiligentCore#API256008")
cpmaddpackage("gh:g-truc/glm#1.0.1")
cpmaddpackage("gh:ocornut/imgui@1.92.1")

add_subdirectory(libs/bloxr-app-foundation)
add_subdirectory(libs/bloxr-engine)
add_subdirectory(src/bloxr-editor)
