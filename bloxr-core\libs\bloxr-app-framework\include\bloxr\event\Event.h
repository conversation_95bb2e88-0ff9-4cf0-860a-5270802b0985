#ifndef BLOXR_APP_EVENT_EVENT_H
#define BLOXR_APP_EVENT_EVENT_H

#include <string_view>

#include <magic_enum/magic_enum.hpp>

namespace bloxr {
namespace app {
namespace event {

enum class EventType;

class Event {
 public:
  Event(EventType type) : type_(type), handled_(false) {}

  EventType Type() const { return type_; }
  bool Handled() const { return handled_; }
  void SetHandled() { handled_ = true; }

  std::string_view String() const { return magic_enum::enum_name(type_); }

 private:
  EventType type_;
  bool handled_ = false;
};

}  // namespace event
}  // namespace app
}  // namespace bloxr

#endif