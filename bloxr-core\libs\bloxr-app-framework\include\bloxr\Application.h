#ifndef BLOXR_APP_APPLICATION_H
#define BLOXR_APP_APPLICATION_H

#include <concepts>
#include <expected>
#include <memory>
#include <string>
#include <vector>

#include <SDL3/SDL.h>

#include "layer/ApplicationLayer.h"
#include "layer/ApplicationLayerStack.h"

#include "platform/Window.h"

namespace bloxr {
namespace app {

enum class ApplicationError {};

struct ApplicationSpec {
  std::string app_name = "Bloxr App";
  uint32_t window_width = 1920;
  uint32_t window_height = 1080;
  uint32_t window_min_width = 800;
  uint32_t window_min_height = 600;
  bool window_resizable = true;
  bool vsync = false;
  bool headless = false;
};

struct ApplicationFrameStats {
  float_t frame_time_last = 0.0F;
  float_t frame_time_delta = 0.0F;
  float_t frame_time_smooth = 0.0F;
};

class Application {
 public:
  Application(const ApplicationSpec& spec = ApplicationSpec());
  ~Application();

  void Run();
  void Close();

  template <typename T>
    requires std::derived_from<T, bloxr::app::layer::ApplicationLayer>
  void PushLayer() {
    layer_stack_.PushLayer<T>();
  }
  void PushLayer(
      const std::shared_ptr<bloxr::app::layer::ApplicationLayer>& layer) {
    layer_stack_.PushLayer(layer);
    layer->OnAttach();
  }

  bloxr::app::platform::Window* Window() { return window_handle_; }
  const ApplicationSpec& Spec() const { return spec_; }
  const ApplicationFrameStats& FrameStats() const { return frame_stats_; }
  const bool Running() const { return running_; }

 private:
  std::expected<void, ApplicationError> Init();
  void Cleanup();
  std::expected<void, ApplicationError> Tick();

  // Implemented in Respective Modules
  // std::expected<void, ApplicationError> InitWindow();
  // std::expected<void, ApplicationError> InitRenderer();
  // std::expected<void, ApplicationError> InitInput();

 private:
  ApplicationSpec spec_;
  ApplicationFrameStats frame_stats_;
  bool running_ = false;
  bloxr::app::layer::ApplicationLayerStack layer_stack_;
  bloxr::app::platform::Window* window_handle_ = nullptr;
};

/**
 * @brief Create a Application object
 *
 * Implemented in the user code.
 *
 * @return Application*
 */
Application* CreateApplication();

}  // namespace app
}  // namespace bloxr

#endif