#include "bloxr/platform/sdl/SDLWindow.h"

#include <glm/glm.hpp>

#include <SDL3/SDL.h>

namespace bloxr {
namespace app {
namespace platform {
namespace impl {

std::expected<void, WindowError> SDLWindow::Init(const struct WindowSpec&) {}
void SDLWindow::Shutdown() {}

void SDLWindow::PollEvents() {}
void SDLWindow::SwapBuffers() {}

bool SDLWindow::ShouldClose() const {}
void SDLWindow::SetShouldClose(bool value = true) {}

glm::ivec2 SDLWindow::Position() const {}
void SDLWindow::SetPosition(glm::ivec2) {}

glm::uvec2 SDLWindow::Size() const {}
void SDLWindow::SetSize(glm::uvec2) {}

glm::uvec2 SDLWindow::FramebufferSize() const {}

std::string SDLWindow::Title() const {}
void SDLWindow::SetTitle(std::string_view) {}

WindowMode SDLWindow::Mode() const {}
void SetMode(WindowMode) {}

void SetBackgroundColor(glm::vec4 color) {}

}  // namespace impl
}  // namespace platform
}  // namespace app
}  // namespace bloxr