#ifndef BLOXR_APP_PLATFORM_SDL_WINDOW_H
#define BLOXR_APP_PLATFORM_SDL_WINDOW_H

#include <expected>

#include <glm.hpp>

#include <SDL3/SDL.h>

#include "../Window.h"

namespace bloxr {
namespace app {
namespace platform {
namespace impl {

class SDLWindow : public Window {
 public:
  virtual std::expected<void, WindowError> Init(
      const struct WindowSpec&) override;
  virtual void Shutdown() override;

  virtual void PollEvents() override;
  virtual void SwapBuffers() override;

  virtual bool ShouldClose() const override;
  virtual void SetShouldClose(bool value = true) override;

  virtual glm::ivec2 Position() const override;
  virtual void SetPosition(glm::ivec2) override;

  virtual glm::uvec2 Size() const override;
  virtual void SetSize(glm::uvec2) override;

  virtual glm::uvec2 FramebufferSize() const override;

  virtual std::string Title() const override;
  virtual void SetTitle(std::string_view) override;

  virtual WindowMode Mode() const override;
  virtual void SetMode(WindowMode) override;

  virtual void SetBackgroundColor(glm::vec4 color) override;
};

}  // namespace impl
}  // namespace platform
}  // namespace app
}  // namespace bloxr

#endif