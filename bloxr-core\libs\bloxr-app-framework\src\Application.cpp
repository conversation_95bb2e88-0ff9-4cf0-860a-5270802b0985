#include <expected>

#include <spdlog/spdlog.h>

#include <SDL3/SDL.h>

#include "bloxr/Application.h"

namespace bloxr {
namespace app {

Application::Application(const ApplicationSpec& spec) : spec_(spec) { Init(); }

Application::~Application() { Cleanup(); }

std::expected<void, ApplicationError> Application::Init() {}

void Application::Cleanup() {}

void Application::Run() { running_ = true; }
void Application::Close() { running_ = false; }

}  // namespace app
}  // namespace bloxr
