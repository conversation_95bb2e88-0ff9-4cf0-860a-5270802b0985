# Bloxr Core Engine

This module provides Bloxr's 3D Engine, written in C++ and compiled to WebAssembly using Emscipten.

## Notice

If the project fails to build due to long paths or filenames on Windows, run the following commands:

```ps
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "LongPathsEnabled" -Value 1 -PropertyType DWORD -Force
```

```cmd
    git config --system core.longpaths true
```
